import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Menu, X, User, Heart, Camera, LogOut, Settings } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import AdvancedSearch from '@/components/features/AdvancedSearch';
import { useWishlist } from '@/contexts/WishlistContext';
import { useAuth } from '@/contexts/AuthContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const { wishlist } = useWishlist();
  const { currentUser, userProfile, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleAuthNavigation = (path: string) => {
    setIsAuthModalOpen(false);
    navigate(path);
  };

  const navItems = [
    { label: 'Tours', href: '/tours' },
    { label: 'Gallery', href: '/gallery' },
    { label: 'About', href: '/about' },
    { label: 'Contact', href: '/contact' },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo with Savannah Styling */}
          <Link to="/" className="flex items-center space-x-2 md:space-x-3 group">
            <div className="bg-gradient-to-br from-amber-600 via-orange-600 to-red-600 text-white p-2 md:p-3 rounded-xl group-hover:scale-105 transition-all duration-300 shadow-lg">
              <Camera className="h-4 w-4 md:h-6 md:w-6" />
            </div>
            <div>
              <span className="font-bold text-xl md:text-2xl bg-gradient-to-r from-amber-700 to-red-700 bg-clip-text text-transparent">Warriors of Africa Safari</span>
              
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
             <Link
  key={item.href}
  to={item.href}
  className="relative text-amber-800 hover:text-orange-600 font-medium transition-all duration-300 py-2 group text-xs xl:text-sm"
>
                {item.label}
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-amber-600 to-orange-600 transition-all duration-300 group-hover:w-full"></span>
              </Link>
            ))}
            
          
          </nav>

          {/* Search and Actions */}
          <div className="hidden lg:flex items-center space-x-3 xl:space-x-6">
            <div className="hidden xl:block">
              <AdvancedSearch />
            </div>
            
            {/* Wishlist */}
            {currentUser && (
              <Link to="/user-dashboard">
                <Button variant="ghost" size="sm" className="relative hover:bg-orange-100 text-amber-800 hover:text-orange-600 p-2">
                  <Heart className="h-4 w-4 md:h-5 md:w-5" />
                  {wishlist.length > 0 && (
                    <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-full text-xs w-4 h-4 md:w-5 md:h-5 flex items-center justify-center shadow-lg">
                      {wishlist.length}
                    </span>
                  )}
                </Button>
              </Link>
            )}

            {/* User Authentication */}
            {currentUser ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="hover:bg-orange-100 text-amber-800 hover:text-orange-600 text-xs md:text-sm px-2 md:px-3">
                    <User className="h-4 w-4 mr-1 md:mr-2" />
                    <span className="hidden xl:inline">{userProfile?.displayName || currentUser.email}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-gradient-to-br from-amber-50 to-orange-50 border-orange-200">
                  <DropdownMenuItem asChild>
                    <Link to="/user-dashboard" className="flex items-center text-amber-800 hover:text-orange-600">
                      <User className="h-4 w-4 mr-2" />
                      Dashboard
                    </Link>
                  </DropdownMenuItem>
                  {userProfile?.role === 'admin' && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center text-amber-800 hover:text-orange-600">
                        <Settings className="h-4 w-4 mr-2" />
                        Admin Panel
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={handleLogout} className="flex items-center text-amber-800 hover:text-orange-600">
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Dialog open={isAuthModalOpen} onOpenChange={setIsAuthModalOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-full px-3 md:px-6 text-xs md:text-sm">
                    <User className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                    <span className="hidden md:inline">Account</span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="bg-gradient-to-br from-amber-50 to-orange-50 border-orange-200">
                  <DialogHeader>
                    <DialogTitle className="text-amber-800 text-center text-lg md:text-xl">Join Warrior of Africa Safari</DialogTitle>
                  </DialogHeader>
                  <div className="flex flex-col space-y-4 p-4 md:p-6">
                    <Button 
                      onClick={() => handleAuthNavigation('/login')}
                      variant="outline"
                      className="bg-white border-amber-300 text-amber-800 hover:bg-amber-50 hover:border-amber-400 transition-all duration-300"
                    >
                      Sign In
                    </Button>
                    <Button 
                      onClick={() => handleAuthNavigation('/register')}
                      className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg transition-all duration-300"
                    >
                      Create Account
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            )}

            <Link to="/tour-builder">
              <Button className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-3 md:px-6 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all text-xs md:text-sm">
                <span className="hidden md:inline">Plan Safari</span>
                <span className="md:hidden">Plan</span>
              </Button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            className="lg:hidden p-2 rounded-lg hover:bg-orange-100 transition-colors text-amber-800"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X className="h-5 w-5 md:h-6 md:w-6" />
            ) : (
              <Menu className="h-5 w-5 md:h-6 md:w-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 bg-gradient-to-br from-amber-50 to-orange-50 backdrop-blur-md py-4 md:py-6 rounded-b-xl">
            <nav className="flex flex-col space-y-3 md:space-y-4">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  to={item.href}
                  className="text-amber-800 hover:text-orange-600 font-medium transition-colors px-4 py-2 rounded-lg hover:bg-orange-100 text-sm md:text-base"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <div className="px-4 pt-3 md:pt-4 border-t border-gray-200 space-y-3 md:space-y-4">
                <AdvancedSearch />
                <div className="flex flex-col space-y-3">
                  {currentUser ? (
                    <div className="space-y-2">
                      <Link to="/user-dashboard" className="block">
                        <Button variant="ghost" size="sm" className="w-full hover:bg-orange-100 text-amber-800 text-sm justify-start">
                          <User className="h-4 w-4 mr-2" />
                          Dashboard
                        </Button>
                      </Link>
                      {userProfile?.role === 'admin' && (
                        <Link to="/admin" className="block">
                          <Button variant="ghost" size="sm" className="w-full hover:bg-orange-100 text-amber-800 text-sm justify-start">
                            <Settings className="h-4 w-4 mr-2" />
                            Admin Panel
                          </Button>
                        </Link>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleLogout}
                        className="w-full text-amber-800 hover:bg-orange-100 text-sm justify-start"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Logout
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 w-full">
                      <Link to="/login" className="flex-1">
                        <Button variant="outline" size="sm" className="w-full border-amber-300 text-amber-800 hover:bg-amber-50 text-sm">Sign In</Button>
                      </Link>
                      <Link to="/register" className="flex-1">
                        <Button size="sm" className="w-full bg-gradient-to-r from-amber-600 to-orange-600 text-white text-sm">Sign Up</Button>
                      </Link>
                    </div>
                  )}
                </div>
                <Link to="/tour-builder" className="block">
                  <Button className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white text-sm">
                    Plan Safari
                  </Button>
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
